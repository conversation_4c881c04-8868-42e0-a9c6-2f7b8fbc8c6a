<?php
/**
 * Duyurular Sayfası - Market Dashboard
 * Tutor LMS tabanlı duyuru yönetim sistemi
 * SEO uyumlu ve responsive tasarım
 *
 * @package MarketKing
 * @subpackage Dashboard
 * <AUTHOR>
 * @since 1.0.0
 * @version 2.0.0
 */

if (!defined('ABSPATH')) { exit; }

// Tutor LMS fonksiyonlarının mevcut olup olmadığını kontrol et
if (!function_exists('tutor') || !function_exists('tutor_utils')) {
    ?>
    <div class="nk-content">
        <div class="container-fluid">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="alert alert-warning">
                        <?php esc_html_e('Tutor LMS eklentisi aktif değil. Duyuruları yönetmek için Tutor LMS eklentisini aktifleştirin.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
    return;
}

// Kullanıcının instructor olup olmadığını kontrol et
if (!current_user_can(tutor()->instructor_role)) {
    ?>
    <div class="nk-content">
        <div class="container-fluid">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="alert alert-warning">
                        <?php esc_html_e('Duyuru oluşturmak için instructor yetkisine sahip olmanız gerekiyor.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
    return;
}

use TUTOR\Input;
use Tutor\Models\CourseModel;

$current_user_id = get_current_user_id();
if (marketking()->is_vendor_team_member()) {
    $current_user_id = marketking()->get_team_member_parent();
}

$per_page = tutor_utils()->get_option('pagination_per_page', 10);
$paged = max(1, Input::get('current_page', 1, Input::TYPE_INT));

$order_filter = Input::get('order', 'DESC');
$search_filter = Input::get('search', '');

// Announcement's parent.
$course_id = Input::get('course-id', '');
$date_filter = Input::get('date', '');

$year = date('Y', strtotime($date_filter));
$month = date('m', strtotime($date_filter));
$day = date('d', strtotime($date_filter));

$args = array(
    'post_type'      => 'tutor_announcements',
    'post_status'    => 'publish',
    's'              => sanitize_text_field($search_filter),
    'post_parent'    => sanitize_text_field($course_id),
    'posts_per_page' => sanitize_text_field($per_page),
    'paged'          => sanitize_text_field($paged),
    'orderBy'        => 'ID',
    'order'          => sanitize_text_field($order_filter),
);

if (!empty($date_filter)) {
    $args['date_query'] = array(
        array(
            'year'  => $year,
            'month' => $month,
            'day'   => $day,
        ),
    );
}

if (!current_user_can('administrator')) {
    $args['author'] = $current_user_id;
}

$the_query = new WP_Query($args);

// Get courses.
$courses = (current_user_can('administrator')) ? CourseModel::get_courses() : CourseModel::get_courses_by_instructor($current_user_id);
$dashboard_url = trailingslashit(get_page_link(apply_filters('wpml_object_id', get_option('marketking_vendordash_page_setting', 'disabled'), 'post', true)));

// SEO Meta bilgileri
$page_title = esc_html__('Duyurular - Kurs Duyuru Yönetimi', 'marketking-multivendor-marketplace-for-woocommerce');
$page_description = esc_html__('Kurslarınız için duyuru oluşturun, düzenleyin ve öğrencilerinizi bilgilendirin. Etkili iletişim için duyuru yönetim sistemi.', 'marketking-multivendor-marketplace-for-woocommerce');
?>

<!-- SEO Meta Tags -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "<?php echo esc_js($page_title); ?>",
    "description": "<?php echo esc_js($page_description); ?>",
    "url": "<?php echo esc_url($dashboard_url . 'announcements'); ?>",
    "isPartOf": {
        "@type": "WebSite",
        "name": "<?php echo esc_js(get_bloginfo('name')); ?>"
    }
}
</script>

<!-- SEO ve Responsive CSS -->
<style>
/* Responsive optimizasyonlar */
@media (max-width: 768px) {
    .nk-tb-col.tb-col-md,
    .nk-tb-col.tb-col-lg {
        display: none;
    }

    .user-info .tb-lead {
        font-size: 14px;
        line-height: 1.4;
    }

    .modal-dialog.modal-lg {
        margin: 10px;
        max-width: calc(100% - 20px);
    }

    .card-inner .row.g-3 > .col-md-4,
    .card-inner .row.g-3 > .col-md-3,
    .card-inner .row.g-3 > .col-md-2 {
        margin-bottom: 15px;
    }
}

/* Erişilebilirlik iyileştirmeleri */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus durumları */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Duyuru kartları için hover efektleri */
.nk-tb-item:hover {
    background-color: #f8f9fa;
    transition: background-color 0.2s ease;
}

/* Loading durumu için */
.btn[disabled] {
    opacity: 0.6;
    cursor: not-allowed;
}
</style>

<div class="nk-content marketking_dashboard_page" role="main" aria-labelledby="announcements-title">
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h1 id="announcements-title" class="nk-block-title page-title"><?php echo esc_html($page_title); ?></h1>
                            <div class="nk-block-des text-soft">
                                <p><?php echo esc_html($page_description); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bildirim Mesajları -->
                <?php if (isset($_GET['announcement_created']) && $_GET['announcement_created'] == '1') : ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <strong><?php esc_html_e('Başarılı!', 'marketking-multivendor-marketplace-for-woocommerce'); ?></strong>
                        <?php esc_html_e('Duyurunuz başarıyla oluşturuldu ve öğrencilerinize gönderildi.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="<?php esc_attr_e('Bildirimi kapat', 'marketking-multivendor-marketplace-for-woocommerce'); ?>"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($_GET['announcement_error']) && $_GET['announcement_error'] == '1') : ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <strong><?php esc_html_e('Hata!', 'marketking-multivendor-marketplace-for-woocommerce'); ?></strong>
                        <?php esc_html_e('Duyuru oluşturulurken bir hata oluştu. Lütfen tekrar deneyin.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="<?php esc_attr_e('Bildirimi kapat', 'marketking-multivendor-marketplace-for-woocommerce'); ?>"></button>
                    </div>
                <?php endif; ?>

                <!-- Duyuru Oluşturma Kartı -->
                <section class="nk-block" aria-labelledby="create-announcement-title">
                    <div class="card card-bordered">
                        <div class="card-inner">
                            <div class="row align-items-center">
                                <div class="col-lg-1">
                                    <div class="nk-block-icon" aria-hidden="true">
                                        <em class="icon ni ni-bell text-primary" style="font-size: 2rem;"></em>
                                    </div>
                                </div>
                                <div class="col-lg-8">
                                    <div class="nk-block-content">
                                        <h2 id="create-announcement-title" class="h6 text-muted"><?php esc_html_e('Duyuru Oluştur', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h2>
                                        <h3 class="h5 title"><?php esc_html_e('Kursunuzdaki tüm öğrencileri bilgilendirin', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h3>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="nk-block-content text-right">
                                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#tutor_announcement_new">
                                            <em class="icon ni ni-plus"></em>
                                            <span><?php esc_html_e('Yeni Duyuru', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="nk-block">
                    <div class="card card-bordered">
                        <div class="card-inner">
                            <form method="get" class="row g-3">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label"><?php esc_html_e('Kurslar', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                                        <select class="form-select" name="course-id">
                                            <option value=""><?php esc_html_e('Tüm Kurslar', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                                            <?php if ($courses) : ?>
                                                <?php foreach ($courses as $course) : ?>
                                                    <option value="<?php echo esc_attr($course->ID); ?>" <?php selected($course_id, $course->ID); ?>>
                                                        <?php echo esc_html($course->post_title); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label"><?php esc_html_e('Tarih', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                                        <input type="date" class="form-control" name="date" value="<?php echo esc_attr($date_filter); ?>">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label"><?php esc_html_e('Arama', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                                        <input type="text" class="form-control" name="search" value="<?php echo esc_attr($search_filter); ?>" 
                                               placeholder="<?php esc_attr_e('Duyuru ara...', 'marketking-multivendor-marketplace-for-woocommerce'); ?>">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="submit" class="btn btn-primary d-block">
                                            <em class="icon ni ni-search"></em>
                                            <span><?php esc_html_e('Filtrele', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Duyuru Listesi -->
                <section class="nk-block" aria-labelledby="announcements-list-title">
                    <div class="card card-bordered card-stretch">
                        <div class="card-inner-group">
                            <?php if ($the_query->have_posts()) : ?>
                                <div class="card-inner">
                                    <h2 id="announcements-list-title" class="h5 mb-3"><?php esc_html_e('Duyuru Listesi', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h2>
                                    <div class="nk-tb-list nk-tb-ulist" role="table" aria-label="<?php esc_attr_e('Duyuru listesi tablosu', 'marketking-multivendor-marketplace-for-woocommerce'); ?>">
                                        <div class="nk-tb-item nk-tb-head" role="row">
                                            <div class="nk-tb-col" role="columnheader"><span class="sub-text"><?php esc_html_e('Başlık', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span></div>
                                            <div class="nk-tb-col tb-col-md" role="columnheader"><span class="sub-text"><?php esc_html_e('Kurs', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span></div>
                                            <div class="nk-tb-col tb-col-lg" role="columnheader"><span class="sub-text"><?php esc_html_e('Tarih', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span></div>
                                            <div class="nk-tb-col nk-tb-col-tools text-right" role="columnheader">
                                                <div class="dropdown">
                                                    <button type="button" class="btn btn-xs btn-outline-light btn-icon dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" aria-label="<?php esc_attr_e('Duyuru işlemleri', 'marketking-multivendor-marketplace-for-woocommerce'); ?>">
                                                        <em class="icon ni ni-plus" aria-hidden="true"></em>
                                                    </button>
                                                    <div class="dropdown-menu dropdown-menu-right">
                                                        <ul class="link-list-opt no-bdr">
                                                            <li><a href="#" data-toggle="modal" data-target="#tutor_announcement_new"><em class="icon ni ni-plus" aria-hidden="true"></em><span><?php esc_html_e('Yeni Duyuru', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span></a></li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <?php while ($the_query->have_posts()) : $the_query->the_post(); ?>
                                            <?php
                                            $announcement_id = get_the_ID();
                                            $course_title = get_the_title(get_post_field('post_parent', $announcement_id));
                                            $announcement_date = get_the_date('d M Y', $announcement_id);
                                            $announcement_content = wp_trim_words(get_the_content(), 20, '...');
                                            ?>
                                            <div class="nk-tb-item" role="row">
                                                <div class="nk-tb-col" role="cell">
                                                    <div class="user-card">
                                                        <div class="user-info">
                                                            <h3 class="tb-lead h6"><?php echo esc_html(get_the_title()); ?></h3>
                                                            <p class="fs-12px text-soft mb-0"><?php echo esc_html($announcement_content); ?></p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="nk-tb-col tb-col-md" role="cell">
                                                    <span class="tb-amount"><?php echo esc_html($course_title ? $course_title : __('Genel', 'marketking-multivendor-marketplace-for-woocommerce')); ?></span>
                                                </div>
                                                <div class="nk-tb-col tb-col-lg" role="cell">
                                                    <time class="tb-date" datetime="<?php echo esc_attr(get_the_date('c', $announcement_id)); ?>"><?php echo esc_html($announcement_date); ?></time>
                                                </div>
                                                <div class="nk-tb-col nk-tb-col-tools">
                                                    <ul class="nk-tb-actions gx-1">
                                                        <li>
                                                            <div class="drodown">
                                                                <a href="#" class="dropdown-toggle btn btn-icon btn-trigger" data-toggle="dropdown">
                                                                    <em class="icon ni ni-more-h"></em>
                                                                </a>
                                                                <div class="dropdown-menu dropdown-menu-right">
                                                                    <ul class="link-list-opt no-bdr">
                                                                        <li><a href="<?php echo esc_url(admin_url('post.php?post=' . $announcement_id . '&action=edit')); ?>" target="_blank">
                                                                            <em class="icon ni ni-edit"></em><span><?php esc_html_e('Düzenle', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                                        </a></li>
                                                                        <li><a href="#" class="delete-announcement" data-id="<?php echo esc_attr($announcement_id); ?>">
                                                                            <em class="icon ni ni-trash"></em><span><?php esc_html_e('Sil', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                                        </a></li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        <?php endwhile; ?>
                                    </div>
                                </div>
                                
                                <!-- Pagination -->
                                <?php
                                $total_posts = $the_query->found_posts;
                                if ($total_posts > $per_page) {
                                    $total_pages = ceil($total_posts / $per_page);
                                    if ($total_pages > 1) {
                                        ?>
                                        <div class="card-inner">
                                            <div class="nk-block-between-md g-3">
                                                <div class="g">
                                                    <ul class="pagination justify-content-center justify-content-md-start">
                                                        <?php
                                                        $current_url = $dashboard_url . 'tutor-announcements';
                                                        $query_args = array();
                                                        if ($course_id) $query_args['course-id'] = $course_id;
                                                        if ($date_filter) $query_args['date'] = $date_filter;
                                                        if ($search_filter) $query_args['search'] = $search_filter;
                                                        if ($order_filter !== 'DESC') $query_args['order'] = $order_filter;
                                                        
                                                        // Previous page
                                                        if ($paged > 1) {
                                                            $prev_page = $paged - 1;
                                                            $prev_args = $query_args;
                                                            if ($prev_page > 1) $prev_args['current_page'] = $prev_page;
                                                            $prev_url = add_query_arg($prev_args, $current_url);
                                                            ?>
                                                            <li class="page-item">
                                                                <a class="page-link" href="<?php echo esc_url($prev_url); ?>">
                                                                    <em class="icon ni ni-chevrons-left"></em>
                                                                </a>
                                                            </li>
                                                            <?php
                                                        }
                                                        
                                                        // Page numbers
                                                        for ($i = 1; $i <= $total_pages; $i++) {
                                                            $page_args = $query_args;
                                                            if ($i > 1) $page_args['current_page'] = $i;
                                                            $page_url = add_query_arg($page_args, $current_url);
                                                            ?>
                                                            <li class="page-item<?php echo $i == $paged ? ' active' : ''; ?>">
                                                                <a class="page-link" href="<?php echo esc_url($page_url); ?>"><?php echo esc_html($i); ?></a>
                                                            </li>
                                                            <?php
                                                        }
                                                        
                                                        // Next page
                                                        if ($paged < $total_pages) {
                                                            $next_page = $paged + 1;
                                                            $next_args = $query_args;
                                                            $next_args['current_page'] = $next_page;
                                                            $next_url = add_query_arg($next_args, $current_url);
                                                            ?>
                                                            <li class="page-item">
                                                                <a class="page-link" href="<?php echo esc_url($next_url); ?>">
                                                                    <em class="icon ni ni-chevrons-right"></em>
                                                                </a>
                                                            </li>
                                                            <?php
                                                        }
                                                        ?>
                                                    </ul>
                                                </div>
                                                <div class="g">
                                                    <div class="pagination-goto d-flex justify-content-center justify-content-md-end gx-3">
                                                        <div><?php printf(esc_html__('Sayfa %1$d / %2$d', 'marketking-multivendor-marketplace-for-woocommerce'), $paged, $total_pages); ?></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php
                                    }
                                }
                                ?>
                            <?php else : ?>
                                <div class="card-inner text-center">
                                    <div class="nk-empty-state">
                                        <div class="nk-empty-state-icon">
                                            <em class="icon ni ni-bell"></em>
                                        </div>
                                        <h4 class="nk-empty-state-title"><?php esc_html_e('Henüz duyuru bulunamadı', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h4>
                                        <p class="nk-empty-state-text"><?php esc_html_e('İlk duyurunuzu oluşturmak için "Yeni Duyuru" butonuna tıklayın.', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>
                                        <div class="nk-empty-state-action">
                                            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#tutor_announcement_new">
                                                <em class="icon ni ni-plus"></em>
                                                <span><?php esc_html_e('Yeni Duyuru Oluştur', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Announcement Modal -->
<div class="modal fade" id="tutor_announcement_new" tabindex="-1" role="dialog" aria-labelledby="announcement-modal-title" aria-describedby="announcement-modal-description">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="announcement-modal-title" class="modal-title h5"><?php esc_html_e('Yeni Duyuru Oluştur', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h2>
                <button type="button" class="close" data-dismiss="modal" aria-label="<?php esc_attr_e('Modalı kapat', 'marketking-multivendor-marketplace-for-woocommerce'); ?>">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="post" action="<?php echo esc_url(admin_url('admin-post.php')); ?>" id="tutor_announcement_form" novalidate>
                <div class="modal-body">
                    <p id="announcement-modal-description" class="text-muted mb-4"><?php esc_html_e('Kursunuzdaki öğrencilere duyuru göndermek için aşağıdaki formu doldurun.', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>

                    <?php wp_nonce_field('tutor_create_announcement_nonce', 'tutor_create_announcement_nonce_field'); ?>
                    <input type="hidden" name="action" value="tutor_create_announcement">
                    <input type="hidden" name="redirect_to" value="<?php echo esc_url($dashboard_url . 'announcements'); ?>">

                    <div class="row g-3">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="announcement_course" class="form-label"><?php esc_html_e('Kurs Seçin', 'marketking-multivendor-marketplace-for-woocommerce'); ?> <span class="text-danger" aria-label="<?php esc_attr_e('Zorunlu alan', 'marketking-multivendor-marketplace-for-woocommerce'); ?>">*</span></label>
                                <select id="announcement_course" class="form-select" name="announcement_course" required aria-describedby="course-help">
                                    <option value=""><?php esc_html_e('Kurs Seçin', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                                    <?php if ($courses) : ?>
                                        <?php foreach ($courses as $course) : ?>
                                            <option value="<?php echo esc_attr($course->ID); ?>">
                                                <?php echo esc_html($course->post_title); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                                <small id="course-help" class="form-text text-muted"><?php esc_html_e('Duyurunun gönderileceği kursu seçin', 'marketking-multivendor-marketplace-for-woocommerce'); ?></small>
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <div class="form-group">
                                <label for="announcement_title" class="form-label"><?php esc_html_e('Duyuru Başlığı', 'marketking-multivendor-marketplace-for-woocommerce'); ?> <span class="text-danger" aria-label="<?php esc_attr_e('Zorunlu alan', 'marketking-multivendor-marketplace-for-woocommerce'); ?>">*</span></label>
                                <input type="text" id="announcement_title" class="form-control" name="announcement_title" required
                                       placeholder="<?php esc_attr_e('Duyuru başlığını girin', 'marketking-multivendor-marketplace-for-woocommerce'); ?>"
                                       aria-describedby="title-help" maxlength="200">
                                <small id="title-help" class="form-text text-muted"><?php esc_html_e('Duyurunuzun açıklayıcı bir başlığını girin (maksimum 200 karakter)', 'marketking-multivendor-marketplace-for-woocommerce'); ?></small>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="form-group">
                                <label for="announcement_content" class="form-label"><?php esc_html_e('Duyuru İçeriği', 'marketking-multivendor-marketplace-for-woocommerce'); ?> <span class="text-danger" aria-label="<?php esc_attr_e('Zorunlu alan', 'marketking-multivendor-marketplace-for-woocommerce'); ?>">*</span></label>
                                <textarea id="announcement_content" class="form-control" name="announcement_content" rows="6" required
                                          placeholder="<?php esc_attr_e('Duyuru içeriğini girin', 'marketking-multivendor-marketplace-for-woocommerce'); ?>"
                                          aria-describedby="content-help" maxlength="2000"></textarea>
                                <small id="content-help" class="form-text text-muted"><?php esc_html_e('Öğrencilerinize iletmek istediğiniz mesajı detaylı bir şekilde yazın (maksimum 2000 karakter)', 'marketking-multivendor-marketplace-for-woocommerce'); ?></small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-light" data-dismiss="modal"><?php esc_html_e('İptal', 'marketking-multivendor-marketplace-for-woocommerce'); ?></button>
                    <button type="submit" class="btn btn-primary" aria-describedby="submit-help">
                        <em class="icon ni ni-plus" aria-hidden="true"></em>
                        <span><?php esc_html_e('Duyuru Oluştur', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                    </button>
                    <small id="submit-help" class="sr-only"><?php esc_html_e('Duyuruyu oluşturmak için bu butona tıklayın', 'marketking-multivendor-marketplace-for-woocommerce'); ?></small>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Delete announcement
    $('.delete-announcement').on('click', function(e) {
        e.preventDefault();
        var announcementId = $(this).data('id');
        
        if (confirm('<?php esc_html_e('Bu duyuruyu silmek istediğinizden emin misiniz?', 'marketking-multivendor-marketplace-for-woocommerce'); ?>')) {
            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                    action: 'tutor_delete_announcement',
                    announcement_id: announcementId,
                    nonce: '<?php echo wp_create_nonce('tutor_delete_announcement_nonce'); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('<?php esc_html_e('Duyuru silinirken bir hata oluştu.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>');
                    }
                }
            });
        }
    });
});

// Form validasyonu ve UX iyileştirmeleri
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('tutor_announcement_form');
    const titleInput = document.getElementById('announcement_title');
    const contentTextarea = document.getElementById('announcement_content');
    const courseSelect = document.getElementById('announcement_course');
    const submitBtn = form.querySelector('button[type="submit"]');

    // Karakter sayacı
    function updateCharacterCount(element, maxLength) {
        const remaining = maxLength - element.value.length;
        let helpElement = element.nextElementSibling;
        if (helpElement && helpElement.classList.contains('form-text')) {
            const originalText = helpElement.textContent;
            const countText = ` (${remaining} karakter kaldı)`;
            if (!originalText.includes('karakter kaldı')) {
                helpElement.textContent = originalText + countText;
            } else {
                helpElement.textContent = originalText.replace(/\s*\(\d+\s*karakter\s*kaldı\)/, countText);
            }

            // Uyarı renkleri
            if (remaining < 50) {
                helpElement.classList.add('text-warning');
                helpElement.classList.remove('text-muted');
            } else {
                helpElement.classList.add('text-muted');
                helpElement.classList.remove('text-warning');
            }
        }
    }

    // Karakter sayacı event listeners
    if (titleInput) {
        titleInput.addEventListener('input', function() {
            updateCharacterCount(this, 200);
        });
    }

    if (contentTextarea) {
        contentTextarea.addEventListener('input', function() {
            updateCharacterCount(this, 2000);
        });
    }

    // Form validasyonu
    if (form) {
        form.addEventListener('submit', function(e) {
            let isValid = true;
            const errors = [];

            // Kurs seçimi kontrolü
            if (!courseSelect.value) {
                errors.push('Lütfen bir kurs seçin.');
                courseSelect.classList.add('is-invalid');
                isValid = false;
            } else {
                courseSelect.classList.remove('is-invalid');
            }

            // Başlık kontrolü
            if (!titleInput.value.trim()) {
                errors.push('Duyuru başlığı gereklidir.');
                titleInput.classList.add('is-invalid');
                isValid = false;
            } else if (titleInput.value.length > 200) {
                errors.push('Başlık 200 karakterden uzun olamaz.');
                titleInput.classList.add('is-invalid');
                isValid = false;
            } else {
                titleInput.classList.remove('is-invalid');
            }

            // İçerik kontrolü
            if (!contentTextarea.value.trim()) {
                errors.push('Duyuru içeriği gereklidir.');
                contentTextarea.classList.add('is-invalid');
                isValid = false;
            } else if (contentTextarea.value.length > 2000) {
                errors.push('İçerik 2000 karakterden uzun olamaz.');
                contentTextarea.classList.add('is-invalid');
                isValid = false;
            } else {
                contentTextarea.classList.remove('is-invalid');
            }

            if (!isValid) {
                e.preventDefault();
                alert('Lütfen aşağıdaki hataları düzeltin:\n' + errors.join('\n'));
                return false;
            }

            // Loading durumu
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Oluşturuluyor...';
        });
    }

    // Modal açıldığında form temizle
    const modal = document.getElementById('tutor_announcement_new');
    if (modal) {
        modal.addEventListener('show.bs.modal', function() {
            if (form) {
                form.reset();
                // Validation sınıflarını temizle
                form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
                // Submit butonunu resetle
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<em class="icon ni ni-plus" aria-hidden="true"></em><span>Duyuru Oluştur</span>';
                }
            }
        });
    }
});
</script>

<?php
wp_reset_postdata();
?>
